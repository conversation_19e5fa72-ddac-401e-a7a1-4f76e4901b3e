<template>
  <div class="submit-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">提交合同</h1>
      <p class="page-subtitle">上传合同文件并选择审核人员</p>
    </div>

    <!-- 主要内容 -->
    <div class="page-content">
      <div class="submit-container">
        <!-- 提交表单 -->
        <el-card class="submit-card">
          <template #header>
            <div class="card-header">
              <h3>合同信息</h3>
              <el-steps :active="currentStep" finish-status="success" simple>
                <el-step title="上传文件" />
                <el-step title="填写信息" />
                <el-step title="提交审核" />
              </el-steps>
            </div>
          </template>

          <el-form
            ref="submitFormRef"
            :model="submitForm"
            :rules="submitRules"
            label-width="120px"
            size="default"
          >
            <!-- 步骤1：文件上传 -->
            <div v-show="currentStep === 0" class="step-content">
              <!-- 直接PDF文件上传 -->
              <el-form-item label="合同文件" prop="files" required>
                <FileUpload
                  v-model="submitForm.files"
                  :limit="1"
                  accept=".pdf"
                  :max-size="1024 * 1024 * 1024"
                  @success="handleFileUploadSuccess"
                  @error="handleFileUploadError"
                />
                <div class="upload-tips">
                  <el-icon><InfoFilled /></el-icon>
                  <span>请上传PDF格式的合同文件，文件大小不超过1GB</span>
                </div>
              </el-form-item>
            </div>

            <!-- 步骤2：填写信息 -->
            <div v-show="currentStep === 1" class="step-content">
              <el-form-item label="审核级别" prop="review_level" required>
                <el-select
                  v-model="submitForm.review_level"
                  placeholder="请选择审核级别"
                  style="width: 100%"
                  @change="handleReviewLevelChange"
                >
                  <el-option label="县局审核员" value="county_reviewer" />
                </el-select>
                <div class="form-tips">
                  <el-icon><InfoFilled /></el-icon>
                  <span
                    >合同将提交给县局审核员，由县局审核员决定是否需要市局审核</span
                  >
                </div>
              </el-form-item>

              <el-form-item label="审核员" prop="reviewer_id" required>
                <el-select
                  v-model="submitForm.reviewer_id"
                  placeholder="请先选择审核级别"
                  style="width: 100%"
                  :disabled="!submitForm.review_level"
                  :loading="loadingReviewers"
                >
                  <el-option
                    v-for="reviewer in filteredReviewers"
                    :key="reviewer.id"
                    :label="`${reviewer.username} (${getRoleDisplayName(reviewer.role)})`"
                    :value="reviewer.id"
                  />
                </el-select>
                <div class="form-tips">
                  <el-icon><InfoFilled /></el-icon>
                  <span>选择具体的审核员</span>
                </div>
              </el-form-item>

              <el-form-item label="提交说明" prop="submit_note">
                <el-input
                  v-model="submitForm.submit_note"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入合同的相关说明信息（可选）"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </div>

            <!-- 步骤3：确认提交 -->
            <div v-show="currentStep === 2" class="step-content">
              <div class="submit-summary">
                <h4>提交信息确认</h4>
                <div class="summary-item">
                  <span class="label">合同文件：</span>
                  <span class="value">{{ getFileName() }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">文件大小：</span>
                  <span class="value">{{ getFileSize() }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">审核级别：</span>
                  <span class="value">{{ getReviewLevelName() }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">审核员：</span>
                  <span class="value">{{ getReviewerName() }}</span>
                </div>
                <div v-if="submitForm.submit_note" class="summary-item">
                  <span class="label">提交说明：</span>
                  <span class="value">{{ submitForm.submit_note }}</span>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="form-actions">
              <el-button
                v-if="currentStep > 0"
                :disabled="submitting"
                @click="prevStep"
              >
                上一步
              </el-button>

              <el-button
                v-if="currentStep < 2"
                type="primary"
                :disabled="!canNextStep()"
                @click="nextStep"
              >
                下一步
              </el-button>

              <el-button
                v-if="currentStep === 2"
                type="primary"
                :loading="submitting"
                @click="handleSubmit"
              >
                {{ submitting ? "提交中..." : "确认提交" }}
              </el-button>

              <el-button :disabled="submitting" @click="resetForm">
                重置
              </el-button>
            </div>
          </el-form>
        </el-card>

        <!-- 提交成功提示 -->
        <el-card v-if="submitSuccess" class="success-card">
          <div class="success-content">
            <el-icon class="success-icon" :size="64">
              <CircleCheckFilled />
            </el-icon>
            <h3 class="success-title">合同提交成功！</h3>
            <p class="success-description">
              您的合同已成功提交，流水号为：<strong>{{
                successInfo.serial_number
              }}</strong>
            </p>
            <p class="success-tips">
              审核人员将在收到通知后开始审核，您可以在"我的合同"中查看审核进度。
            </p>
            <div class="success-actions">
              <el-button type="primary" @click="goToMyContracts">
                查看我的合同
              </el-button>
              <el-button @click="submitAnother"> 继续提交 </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { InfoFilled, CircleCheckFilled } from "@element-plus/icons-vue";

import FileUpload from "@/components/common/FileUpload.vue";
import { useContracts } from "@/composables/useContracts";
import { contractsAPI } from "@/api/contracts";
import { filesAPI } from "@/api/files";

const router = useRouter();

// 合同管理
const { submitContract, submitting } = useContracts();

// 表单引用
const submitFormRef = ref();

// 当前步骤
const currentStep = ref(0);

// 审核级别选项 - 员工只能选择县局审核员
const reviewLevelOptions = [{ value: "county_reviewer", label: "县局审核员" }];

// 提交成功状态
const submitSuccess = ref(false);
const successInfo = ref({});

// 表单数据 - 默认设置为县级审核员
const submitForm = reactive({
  files: [],
  review_level: "county_reviewer",
  reviewer_id: "",
  submit_note: "",
});

// 审核员相关状态
const allReviewers = ref([]); // 缓存所有审核员
const loadingReviewers = ref(false);
const reviewersLoaded = ref(false); // 标记是否已加载过审核员数据

// 表单验证规则
const submitRules = {
  files: [{ required: true, message: "请上传合同文件", trigger: "change" }],
  review_level: [
    { required: true, message: "请选择审核级别", trigger: "change" },
  ],
  reviewer_id: [{ required: true, message: "请选择审核员", trigger: "change" }],
  submit_note: [
    { max: 500, message: "提交说明不能超过500个字符", trigger: "blur" },
  ],
};

// 获取审核级别名称
const getReviewLevelName = () => {
  const option = reviewLevelOptions.find(
    (opt) => opt.value === submitForm.review_level,
  );
  return option ? option.label : "";
};

// 获取审核员名称
const getReviewerName = () => {
  const reviewer = allReviewers.value.find(
    (r) => r.id === submitForm.reviewer_id,
  );
  return reviewer
    ? `${reviewer.username} (${getRoleDisplayName(reviewer.role)})`
    : "";
};

// 获取角色显示名称
const getRoleDisplayName = (role) => {
  const roleMap = {
    admin: "管理员",
    county_reviewer: "县级审核员",
    city_reviewer: "市级审核员",
  };
  return roleMap[role] || role;
};

// 计算属性：根据审核级别动态过滤审核员
const filteredReviewers = computed(() => {
  if (!submitForm.review_level || !allReviewers.value.length) {
    return [];
  }

  if (submitForm.review_level === "county_reviewer") {
    return allReviewers.value.filter(
      (reviewer) => reviewer.role === "county_reviewer",
    );
  }

  return allReviewers.value;
});

// 预加载所有审核员数据
const loadAllReviewers = async () => {
  if (reviewersLoaded.value) return; // 已加载过，直接返回

  try {
    loadingReviewers.value = true;
    // 不传level参数，获取所有审核员
    const response = await contractsAPI.getReviewers();
    if (response.success) {
      allReviewers.value = response.data;
      reviewersLoaded.value = true;
    }
  } catch (error) {
    console.error("获取审核员列表失败:", error);
    ElMessage.error("获取审核员列表失败");
  } finally {
    loadingReviewers.value = false;
  }
};

// 处理审核级别变化
const handleReviewLevelChange = async (level) => {
  // 清空当前选择的审核员
  submitForm.reviewer_id = "";

  // 确保已加载所有审核员数据（如果还没加载的话）
  if (!reviewersLoaded.value) {
    await loadAllReviewers();
  }

  // 计算属性会自动根据审核级别过滤审核员，无需手动过滤
};

// 检查是否可以进入下一步
const canNextStep = () => {
  switch (currentStep.value) {
    case 0:
      return submitForm.files.length > 0;
    case 1:
      return submitForm.review_level;
    default:
      return false;
  }
};

// 下一步
const nextStep = async () => {
  if (currentStep.value === 0) {
    // 验证文件上传
    if (submitForm.files.length === 0) {
      ElMessage.warning("请先上传合同文件");
      return;
    }
  } else if (currentStep.value === 1) {
    // 验证表单
    try {
      await submitFormRef.value.validateField([
        "review_level",
        "reviewer_id",
        "submit_note",
      ]);
    } catch (error) {
      return;
    }
  }

  currentStep.value++;
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 处理文件上传成功
const handleFileUploadSuccess = (fileInfo) => {
  // 文件上传成功，但不自动跳转，让用户确认后手动点击下一步
  ElMessage.success("文件上传成功，请确认后点击下一步");
};

// 处理文件上传失败 - 简化错误处理，依赖全局错误处理器
const handleFileUploadError = (error) => {
  // 全局错误处理器会处理网络错误，这里只需要给用户友好提示
  ElMessage.error("文件上传失败，请重试");
};

// 获取文件名
const getFileName = () => {
  return submitForm.files.length > 0 ? submitForm.files[0].name : "";
};

// 获取文件大小
const getFileSize = () => {
  if (submitForm.files.length > 0) {
    return filesAPI.formatFileSize(submitForm.files[0].size);
  }
  return "";
};

// 提交合同
const handleSubmit = async () => {
  try {
    // 最终验证
    await submitFormRef.value.validate();

    if (submitForm.files.length === 0) {
      ElMessage.error("请上传合同文件");
      return;
    }

    const file = submitForm.files[0];

    // 构建提交数据
    const contractData = {
      filename: file.name || file.originalName, // 原始文件名
      file_path: file.filename, // 服务器生成的文件名
      file_size: file.size,
      review_level: submitForm.review_level,
      reviewer_id: submitForm.reviewer_id,
      submit_note: submitForm.submit_note || null,
    };

    // 提交合同
    const result = await submitContract(contractData);

    if (result) {
      successInfo.value = result;
      submitSuccess.value = true;
      currentStep.value = 0;

      // 延迟重置表单，避免与成功状态更新冲突
      nextTick(() => {
        resetForm();
      });
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("提交合同失败:", error);
    }
    ElMessage.error(error.message || "提交合同失败");
  }
};

// 重置表单
const resetForm = () => {
  // 先清除验证状态，避免触发验证错误
  submitFormRef.value?.clearValidate();

  // 重置表单字段
  nextTick(() => {
    submitFormRef.value?.resetFields();
    submitForm.files = [];
    submitForm.review_level = "";
    submitForm.reviewer_id = "";
    submitForm.submit_note = "";
    // 不需要清空 allReviewers，保持缓存
    currentStep.value = 0;
    submitSuccess.value = false;
    successInfo.value = {};
  });
};

// 跳转到我的合同
const goToMyContracts = () => {
  router.push("/my-contracts");
};

// 继续提交
const submitAnother = () => {
  resetForm();
};

// 组件挂载时的初始化
onMounted(() => {
  // 预加载审核员数据，提升用户体验
  loadAllReviewers();
});
</script>

<style scoped>
.submit-page {
  min-height: 100%;
  background: #f5f5f5;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.page-content {
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 120px); /* 减去header高度 */
}

.submit-container {
  max-width: 800px;
  width: 100%;
  margin: 0;
}

.submit-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  min-height: 40px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.step-content {
  min-height: 80px;
  padding: 8px 0;
}

.upload-mode-tips,
.upload-tips,
.form-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.upload-mode-tips .el-icon,
.upload-tips .el-icon,
.form-tips .el-icon {
  color: #409eff;
}

.submit-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
}

.submit-summary h4 {
  margin: 0 0 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.summary-item {
  display: flex;
  margin-bottom: 6px;
  align-items: flex-start;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item .label {
  width: 100px;
  color: #606266;
  font-weight: 500;
  flex-shrink: 0;
}

.summary-item .value {
  color: #303133;
  word-break: break-all;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e4e7ed;
}

.success-card {
  border: 2px solid #67c23a;
}

.success-content {
  text-align: center;
  padding: 40px 20px;
}

.success-icon {
  color: #67c23a;
  margin-bottom: 16px;
}

.success-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px;
}

.success-description {
  font-size: 16px;
  color: #606266;
  margin: 0 0 8px;
}

.success-tips {
  font-size: 14px;
  color: #909399;
  margin: 0 0 24px;
}

.success-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-content {
    padding: 16px;
    min-height: calc(100vh - 100px); /* 移动端调整高度 */
  }

  .card-header {
    flex-direction: column;
    gap: 8px;
    align-items: center;
    text-align: center;
  }

  .form-actions {
    flex-direction: column;
  }

  .success-actions {
    flex-direction: column;
  }

  .summary-item {
    flex-direction: column;
    gap: 4px;
  }

  .summary-item .label {
    width: auto;
  }
}

/* Element Plus 组件样式覆盖 - 压缩高度 */
:deep(.el-card__header) {
  padding: 12px 20px;
}

:deep(.el-card__body) {
  padding: 12px 20px;
}

:deep(.el-form-item) {
  margin-bottom: 12px;
}

:deep(.el-steps--simple) {
  margin: 0;
  display: flex;
  align-items: center;
}

:deep(.el-step) {
  display: flex;
  align-items: center;
}

:deep(.el-step__main) {
  padding-left: 6px;
  display: flex;
  align-items: center;
}

:deep(.el-step__head) {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-step__icon) {
  width: 20px;
  height: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-step__title) {
  font-size: 13px;
  line-height: 20px;
  margin: 0;
}

:deep(.el-step__arrow) {
  margin-top: 0;
  margin-left: 8px;
}

/* 进一步压缩文件上传区域 */
:deep(.el-upload-dragger) {
  padding: 20px 16px !important;
}

:deep(.upload-content) {
  padding: 20px 16px !important;
}

:deep(.upload-icon) {
  font-size: 32px !important;
  margin-bottom: 8px !important;
}

:deep(.upload-title) {
  font-size: 14px !important;
  margin: 0 0 4px !important;
}

:deep(.upload-hint) {
  font-size: 12px !important;
  margin: 0 !important;
}

:deep(.el-form-item__label) {
  line-height: 1.2 !important;
  padding-bottom: 4px !important;
}

:deep(.el-form-item__content) {
  line-height: 1.2 !important;
}
</style>
